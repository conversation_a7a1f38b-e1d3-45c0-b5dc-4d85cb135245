"""
时间解析器正则模式
从 time_parser.py 中迁移的正则表达式模式
"""

import re
import os
import sys

if __name__ == "__main__":
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(script_dir))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)

from app.utils.extractor.time.parser.parser_utils import bracket, bracket_absence, absence
from app.utils.extractor.time.parser.regex_pattern import (
    CHINESE_CHAR_PATTERN, LIMIT_YEAR_STRING,
    LUNAR_YEAR_STRING, YEAR_STRING, MONTH_NUM_STRING, MONTH_STRING,
    BLUR_MONTH_STRING, LUNAR_MONTH_STRING, LIMIT_MONTH_STRING,
    SELF_EVI_LUNAR_MONTH_STRING, WEEK_NUM_STRING, WEEK_STRING,
    DAY_NUM_STRING, DAY_STRING, BLUR_DAY_STRING, LUNAR_SOLAR_DAY_STRING,
    LUNAR_DAY_STRING, SELF_EVI_LUNAR_DAY_STRING, HOUR_STRING, BLUR_HOUR_STRING,
    MIN_SEC_STRING, I, LU, LU_A, TIME_POINT_SUFFIX,
    SOLAR_TERM_STRING, FIXED_SOLAR_FESTIVAL, FIXED_LUNAR_FESTIVAL,
    REGULAR_FOREIGN_FESTIVAL, DELTA_NUM_STRING,
    SINGLE_NUM_STRING, YEAR_DELTA_STRING, SOLAR_SEASON_DELTA_STRING,
    MONTH_DELTA_STRING, WORKDAY_DELTA_STRING, DAY_DELTA_STRING,
    WEEK_DELTA_STRING, HOUR_DELTA_STRING, QUARTER_DELTA_STRING,
    MINUTE_DELTA_STRING, SECOND_DELTA_STRING, DELTA_SUB
)

# 相对时间范围前缀
RELATIVE_TIME_RANGE_PREFIX = "relative_time_range"

# =============== 基础模式 ===============

# 未来时间扩展 单元基，合并或分开是个问题
FUTURE_TIME_UNIT_PATTERN = re.compile('(年|月|周|星期|礼拜|日|号|节|时|点)')
FUTURE_TIME_UNIT_HMS_PATTERN = re.compile('(时|点|分)')

# 中文字符判定
CHINESE_CHAR_PATTERN_COMPILED = re.compile(CHINESE_CHAR_PATTERN)

# =============== 超模糊模式 ===============

# 超模糊 两 模式
SUPER_BLUR_TWO_YMD_PATTERN = re.compile('^前两(天|(个)?月|年)$')
SUPER_BLUR_TWO_HMS_PATTERN = re.compile('^前两((个)?(小时|钟头)|分钟|秒(钟)?)$')

# 超模糊 几天 模式：近几天、最近几天、这些天、这几天、最近这些天、最近这几天
SUPER_BLUR_SEVERAL_DAYS_PATTERN = re.compile('(?:(?:最?近这?)?|这)(?:几天|些天|数天)')

# =============== 时间跨度分割模式 ===============

# 1997.02-2020.12 此类须强制拆分
TIME_SPAN_SEG_STANDARD_YEAR_MONTH_TO_YEAR_MONTH = re.compile(
    r'((17|18|19|20|21)\d{2})[./](1[012]|[0]?\d)[\-]((17|18|19|20|21)\d{2})([./](1[012]|[0]?\d))?')

TIME_SPAN_NO_SEG_STANDARD_YEAR_MONTH_DAY = re.compile(
    r'((17|18|19|20|21)\d{2})\-(1[012]|[0]?\d)[\-./](30|31|[012]?\d)|'
    r'((17|18|19|20|21)\d{2})[\-./](1[012]|[0]?\d)\-(30|31|[012]?\d)|'
    r'(^\d)(1[012]|[0]?\d)\-(30|31|[012]?\d)(^\d)')

# =============== 标准时间模式 ===============

# `标准数字 年、月、日`：`2016-05-22`、`1987.12-3`
STANDARD_YEAR_MONTH_DAY_PATTERN = re.compile(
    r'((17|18|19|20|21)\d{2})[\-./](1[012]|[0]?\d)([\-./](30|31|[012]?\d))?[ \t\u3000\-./]?|'
    r'(1[012]|[0]?\d)[·\-](30|31|[012]?\d)')

# `标准数字 年`：`2018`
STANDARD_YEAR_PATTERN = re.compile(r'(17|18|19|20|21)\d{2}')

# =============== 年月日模式 ===============

# `年、月、日`：`2009年5月31日`、`一九九二年四月二十五日`
YEAR_MONTH_DAY_PATTERN = re.compile(
    ''.join([bracket(YEAR_STRING), bracket_absence(MONTH_STRING), bracket_absence(DAY_STRING),
             absence(TIME_POINT_SUFFIX), I,
             bracket(MONTH_STRING), bracket_absence(DAY_STRING), absence(TIME_POINT_SUFFIX)]))

# `年、季度`：`2018年前三季度`
YEAR_SOLAR_SEASON_PATTERN = re.compile(
    ''.join([bracket_absence(YEAR_STRING), r'(([第前后头]?[一二三四1-4两]|首)(个)?季度[初中末]?)']))

# `限定年、季度`：`2018年前三季度`
LIMIT_YEAR_SOLAR_SEASON_PATTERN = re.compile(
    ''.join([bracket(LIMIT_YEAR_STRING), r'(([第前后头]?[一二三四1-4两]|首)(个)?季度[初中末]?)']))

# `限定季度`：`上季度`
LIMIT_SOLAR_SEASON_PATTERN = re.compile(r'([上下](个)?|本|这)季度[初中末]?')

# `年、范围月`：`2018年前三个月`
YEAR_SPAN_MONTH_PATTERN = re.compile(
    ''.join([bracket_absence(YEAR_STRING),
             r'(([第前后头]', MONTH_NUM_STRING, r'|首)(个)?月(份)?)']))

# `年、范围月`：`2018年前三个月`
LIMIT_YEAR_SPAN_MONTH_PATTERN = re.compile(
    ''.join([bracket(LIMIT_YEAR_STRING),
             r'(([第前后头]', MONTH_NUM_STRING, r'|首)(个)?月(份)?)']))

# `年、模糊月 时间段`：`1988年末`、`07年暑假`
YEAR_BLUR_MONTH_PATTERN = re.compile(
    ''.join([bracket(YEAR_STRING), r'(年)?(初|[一]开年|伊始|末|尾|终|底)|',
             bracket_absence(YEAR_STRING), r'([上|下]半年|[暑寒][假期]|[前中后]期)']))

# `限定月、日`： `下个月9号`
LIMIT_MONTH_DAY_PATTERN = re.compile(
    ''.join([bracket(LIMIT_MONTH_STRING), bracket_absence(DAY_STRING)]))

# `限定月、模糊日`： `下个月末`
LIMIT_MONTH_BLUR_DAY_PATTERN = re.compile(''.join([bracket(LIMIT_MONTH_STRING), BLUR_DAY_STRING]))

# `限定月`： `下个月`
LIMIT_MONTH_PATTERN = re.compile(LIMIT_MONTH_STRING)

# `模糊年、模糊月 时间段`：`1988年末`、`07年暑假`
LIMIT_YEAR_BLUR_MONTH_PATTERN = re.compile(
    ''.join(['(', bracket(LIMIT_YEAR_STRING), '(年)?|年)', BLUR_MONTH_STRING]))

# `指代年、月、日`：`今年9月`、`前年9月2日`
LIMIT_YEAR_MONTH_DAY_PATTERN = re.compile(
    ''.join([bracket(LIMIT_YEAR_STRING), bracket_absence(MONTH_STRING),
             bracket_absence(DAY_STRING), absence(TIME_POINT_SUFFIX)]))

# `指代限定年`：`两年后`、`20多年前`
BLUR_YEAR_PATTERN = re.compile(
    r'(\d{1,4}|[一二两三四五六七八九十百千]+)[几多]?年(半)?(多)?[以之]?[前后]|'
    r'半年(多)?[以之]?[前|后]|'
    r'几[十百千](多)?年[以之]?[前|后]')

# `世纪、年代`：`20世纪二十年代`
CENTURY_YEAR_PATTERN = re.compile(
    r'(公元(前)?)?(\d{1,2}|((二)?十)?[一二三四五六七八九]|(二)?十|上)世纪'
    r'((\d0|[一二三四五六七八九]十)年代)?([初中末](期)?|前期|后期)?|'
    r'(\d0|[一二三四五六七八九]十)年代([初中末](期)?|前期|后期)?')

# `（年、月）、枚举日`：十月21号、22号、23号， 9月1日，2日，3日
ENUM_DAY_PATTERN = re.compile(
    ''.join([bracket_absence(YEAR_STRING), bracket_absence(MONTH_STRING),
             bracket(DAY_STRING), bracket('[、，, ]' + bracket(DAY_STRING)), '+']))

# =============== 农历模式 ===============

# `农历年、月、日`：二〇一七年农历正月十九
LUNAR_YEAR_MONTH_DAY_PATTERN = re.compile(
    ''.join([
        # 2012年9月初十/9月初十/初十， `日`自证农历
        LU_A, bracket_absence(LUNAR_YEAR_STRING), LU_A, bracket_absence(LUNAR_MONTH_STRING),
        SELF_EVI_LUNAR_DAY_STRING, I,

        # 2012年冬月/2012年冬月初十/冬月初十/冬月， `月`自证农历
        LU_A, bracket_absence(LUNAR_YEAR_STRING), LU_A,
        bracket(SELF_EVI_LUNAR_MONTH_STRING), absence(LUNAR_SOLAR_DAY_STRING), I,

        # 强制标明农历，原因在于农历和公历的混淆，非常复杂
        LU, bracket(LUNAR_YEAR_STRING), bracket(LUNAR_MONTH_STRING), I,  # 农历二零一二年九月
        bracket(LUNAR_YEAR_STRING), LU, bracket(LUNAR_MONTH_STRING), I,  # 二零一二年农历九月

        # 二月十五/2月十五/农历九月十二， `日`后无`日`字，自证农历
        LU_A, bracket(LUNAR_MONTH_STRING), LUNAR_DAY_STRING, I,

        LU, bracket(LUNAR_MONTH_STRING), I,  # 农历九月
        LU, bracket(LUNAR_YEAR_STRING), I,  # 农历二〇一二年
        LU, LUNAR_DAY_STRING]))  # 农历初十

LUNAR_LIMIT_YEAR_MONTH_DAY_PATTERN = re.compile(
    ''.join([
        # 非强制`农历`，根据 `日` 得知为农历日期
        LU_A, bracket(LIMIT_YEAR_STRING), LU_A, bracket(LUNAR_MONTH_STRING),
        SELF_EVI_LUNAR_DAY_STRING, I,  # 今年9月初十

        # 非强制`农历`，根据 `月` 得知为农历日期
        bracket(LIMIT_YEAR_STRING), LU_A, bracket(SELF_EVI_LUNAR_MONTH_STRING),
        absence(LUNAR_SOLAR_DAY_STRING), I,  # 2012年冬月/2012年冬月初十/冬月初十/冬月

        # 去年二月十五/去年2月十五/明年农历九月十二， `日`后无`日`字，自证农历
        LU_A, bracket(LIMIT_YEAR_STRING), LU_A,
        bracket(LUNAR_MONTH_STRING), LUNAR_DAY_STRING, I,

        # 强制标明`农历`，原因在于农历和公历的混淆
        LU, bracket(LIMIT_YEAR_STRING), I,  # 农历二〇一二年
        LU, bracket(LIMIT_YEAR_STRING), bracket(LUNAR_MONTH_STRING), I,  # 农历二零一二年九月
        bracket(LIMIT_YEAR_STRING), LU, bracket(LUNAR_MONTH_STRING)]))  # 二零一二年农历九月

# 年、（农历）季节
YEAR_LUNAR_SEASON_PATTERN = re.compile(
    ''.join([bracket_absence(LUNAR_YEAR_STRING),
             r'[春夏秋冬][季天]|', bracket(LUNAR_YEAR_STRING), r'[春夏秋冬]']))

# 限定年、（农历）季节
LIMIT_YEAR_LUNAR_SEASON_PATTERN = re.compile(
    ''.join([bracket(LIMIT_YEAR_STRING), r'[春夏秋冬][季天]?']))

# 年、节气
YEAR_24ST_PATTERN = re.compile(''.join([bracket_absence(LUNAR_YEAR_STRING), SOLAR_TERM_STRING]))

# 年、月、模糊日（旬）
YEAR_MONTH_BLUR_DAY_PATTERN = re.compile(
    ''.join([bracket_absence(LUNAR_YEAR_STRING), bracket(MONTH_STRING), BLUR_DAY_STRING]))

# 限定年、月、模糊日（旬）
LIMIT_YEAR_MONTH_BLUR_DAY_PATTERN = re.compile(
    ''.join([bracket(LIMIT_YEAR_STRING), bracket(MONTH_STRING), BLUR_DAY_STRING]))

# =============== 星期模式 ===============

# 星期 （一般不与年月相关联）
STANDARD_WEEK_DAY_PATTERN = re.compile(
    '(上上|上|下下|下|本|这)?(一)?(个)?(周)?' + WEEK_STRING + '[一二三四五六日末天]')

# 星期前后推算
BLUR_WEEK_PATTERN = re.compile(
    '[前后]' + WEEK_NUM_STRING + '(个)?' + WEEK_STRING + I +
    WEEK_NUM_STRING + '(个)?' + WEEK_STRING + '(之)?[前后]' + I +
    '(上上|上|下下|下|本|这)?(一)?(个)?' + WEEK_STRING)

# 月、第n个星期k
LIMIT_WEEK_PATTERN = re.compile(
    ''.join([bracket(MONTH_STRING), '(的)?',
             '第[1-5一二三四五](个)?', WEEK_STRING, '[一二三四五六日末天]']))

# 年、第n个星期
YEAR_WEEK_PATTERN = re.compile(
    ''.join([bracket(YEAR_STRING), '第', bracket(WEEK_NUM_STRING),
             '(个)?', WEEK_STRING]))

# 限定年、第n个星期
LIMIT_YEAR_WEEK_PATTERN = re.compile(
    ''.join([bracket(LIMIT_YEAR_STRING), '第', bracket(WEEK_NUM_STRING),
             '(个)?', WEEK_STRING]))

# =============== 特殊日期模式 ===============

# 1月1  此类不全的日期，缺少日
NUM_MONTH_NUM_PATTERN = re.compile(
    ''.join(['^', MONTH_NUM_STRING, '月', r'([12]\d|3[01]|[0]?[1-9])', '$']))

# 限定性`日`
LIMIT_DAY_PATTERN = re.compile(
    r'(前|今|明|同一|当|后|大大前|大大后|大前|大后|昨|次|本)[天日晚]')

# =============== 时分秒模式 ===============

# 时分秒 文字
HOUR_MINUTE_SECOND_PATTERN = re.compile(
    ''.join([absence(BLUR_HOUR_STRING), bracket(HOUR_STRING),
             bracket_absence(MIN_SEC_STRING + '分?'), bracket_absence(MIN_SEC_STRING + '秒'),
             absence(TIME_POINT_SUFFIX), I,
             bracket(MIN_SEC_STRING + '分'), bracket_absence(MIN_SEC_STRING + '秒'),
             absence(TIME_POINT_SUFFIX)]))

# 标准格式`:`分隔时分秒
NUM_HOUR_MINUTE_SECOND_PATTERN = re.compile(
    ''.join([absence(BLUR_HOUR_STRING),
             r'([01]\d|2[01234]|\d)[:：]([012345]\d)([:：]([012345]\d))?',
             absence(TIME_POINT_SUFFIX), r'(时)?', I,
             r'([012345]\d)[:：]([012345]\d)', absence(TIME_POINT_SUFFIX), r'(时)?']))

# 模糊性 `时` 段
BLUR_HOUR_PATTERN = re.compile(BLUR_HOUR_STRING)

# 限定性 `分`
HOUR_LIMIT_MINUTE_PATTERN = re.compile(
    ''.join([absence(BLUR_HOUR_STRING), bracket(HOUR_STRING), r'([123一二三]刻|半)']))

SINGLE_NUM_PATTERN = re.compile(SINGLE_NUM_STRING)

# =============== 时间增量模式 ===============

# 对时间段的描述
EXCEPTION_STANDARD_DELTA_PATTERN = re.compile(
    r'(([12]\d{3}|[一二三四五六七八九零〇]{2}|[一二三四五六七八九零〇]{4})年'
    r')')

AMBIVALENT_DELTA_POINT_PATTERN = re.compile(
    r'(' + DAY_NUM_STRING + '日|'
                            r'\d{2}年)')  # 满足该正则，说明既符合 point，又符合 delta；若非明确指定，默认按 point 处理

DELTA_NUM_PATTERN = re.compile(DELTA_NUM_STRING)

YEAR_DELTA_PATTERN = re.compile(bracket(YEAR_DELTA_STRING))
SOLAR_SEASON_DELTA_PATTERN = re.compile(bracket(SOLAR_SEASON_DELTA_STRING))
MONTH_DELTA_PATTERN = re.compile(bracket(MONTH_DELTA_STRING))
WORKDAY_DELTA_PATTERN = re.compile(bracket(WORKDAY_DELTA_STRING))
DAY_DELTA_PATTERN = re.compile(bracket(DAY_DELTA_STRING))
WEEK_DELTA_PATTERN = re.compile(bracket(WEEK_DELTA_STRING))
HOUR_DELTA_PATTERN = re.compile(bracket(HOUR_DELTA_STRING))
QUARTER_DELTA_PATTERN = re.compile(bracket(QUARTER_DELTA_STRING))
MINUTE_DELTA_PATTERN = re.compile(bracket(MINUTE_DELTA_STRING))
SECOND_DELTA_PATTERN = re.compile(bracket(SECOND_DELTA_STRING))

STANDARD_DELTA_PATTERN = re.compile(
    ''.join(['^(', bracket(YEAR_DELTA_STRING), I,
             bracket(SOLAR_SEASON_DELTA_STRING), I,
             bracket(MONTH_DELTA_STRING), I,
             bracket(WORKDAY_DELTA_STRING), I,
             bracket(DAY_DELTA_STRING), I,
             bracket(WEEK_DELTA_STRING), I,
             bracket(HOUR_DELTA_STRING), I,
             bracket(QUARTER_DELTA_STRING), I,
             bracket(MINUTE_DELTA_STRING), I,
             bracket(SECOND_DELTA_STRING), ')+$']))

# 标准时间增量字符串
STANDARD_DELTA_STRING = ''.join(
    ['(', bracket(YEAR_DELTA_STRING), I,
     bracket(SOLAR_SEASON_DELTA_STRING), I,
     bracket(MONTH_DELTA_STRING), I,
     bracket(WORKDAY_DELTA_STRING), I,
     bracket(DAY_DELTA_STRING), I,
     bracket(WEEK_DELTA_STRING), I,
     bracket(HOUR_DELTA_STRING), I,
     bracket(MINUTE_DELTA_STRING), I,
     bracket(SECOND_DELTA_STRING), ')+'])

WEILAI_DELTA2SPAN_PATTERN = re.compile(
    ''.join(['(未来|今后)(的)?', STANDARD_DELTA_STRING, '[里内]?']))

GUOQU_DELTA2SPAN_PATTERN = re.compile(
    ''.join(['((过去)(的)?|(最)?近)', STANDARD_DELTA_STRING, '[里内]?']))

GUO_DELTA2SPAN_PATTERN = re.compile(
    ''.join(['(再)?(过)', STANDARD_DELTA_STRING]))

LAW_DELTA_PATTERN = re.compile(
    ''.join([DELTA_NUM_STRING, '(年|个月|日|天)(以[上下])',
             bracket_absence(''.join(['[、,，]?', DELTA_NUM_STRING, '(年|个月|日|天)(以下)']))]))

# 将时间段转换为时间点
YEAR_DELTA_POINT_PATTERN = re.compile(''.join([bracket(YEAR_DELTA_STRING), DELTA_SUB]))
SOLAR_SEASON_DELTA_POINT_PATTERN = re.compile(''.join([bracket(SOLAR_SEASON_DELTA_STRING), DELTA_SUB]))
MONTH_DELTA_POINT_PATTERN = re.compile(''.join([bracket(MONTH_DELTA_STRING), DELTA_SUB]))
WORKDAY_DELTA_POINT_PATTERN = re.compile(''.join([bracket(WORKDAY_DELTA_STRING), DELTA_SUB]))
DAY_DELTA_POINT_PATTERN = re.compile(''.join([bracket(DAY_DELTA_STRING), DELTA_SUB]))
WEEK_DELTA_POINT_PATTERN = re.compile(''.join([bracket(WEEK_DELTA_STRING), DELTA_SUB]))
HOUR_DELTA_POINT_PATTERN = re.compile(''.join([bracket(HOUR_DELTA_STRING), DELTA_SUB]))
QUARTER_DELTA_POINT_PATTERN = re.compile(''.join([bracket(QUARTER_DELTA_STRING), DELTA_SUB]))
MINUTE_DELTA_POINT_PATTERN = re.compile(''.join([bracket(MINUTE_DELTA_STRING), DELTA_SUB]))
SECOND_DELTA_POINT_PATTERN = re.compile(''.join([bracket(SECOND_DELTA_STRING), DELTA_SUB]))

YEAR_ORDER_DELTA_POINT_PATTERN = re.compile(''.join([r'第', DELTA_NUM_STRING, r'年']))
DAY_ORDER_DELTA_POINT_PATTERN = re.compile(''.join([r'第', DELTA_NUM_STRING, r'[天日]']))

# =============== 年份相关模式 ===============

YEAR_PATTERN = re.compile(YEAR_STRING[:-1] + r'(?=年)')
LIMIT_YEAR_PATTERN = re.compile(LIMIT_YEAR_STRING[:-1] + r'(?=年)')
BLUR_YEAR_1_PATTERN = re.compile(
    r'([12]?\d{1,4}|(?<!几)[一二两三四五六七八九十百千])[几多]?年(半)?(多)?[以之]?[前后]')
BLUR_YEAR_2_PATTERN = re.compile('半年(多)?[以之]?[前后]')
BLUR_YEAR_3_PATTERN = re.compile('几[十百千](多)?年[以之]?[前后]')

LUNAR_YEAR_PATTERN = re.compile('[甲乙丙丁戊己庚辛壬癸][子丑寅卯辰巳午未申酉戌亥]年')

CENTURY_PATTERN = re.compile(r'(\d{1,2}|((二)?十)?[一二三四五六七八九]|(二)?十|上)(?=世纪)')
DECADE_PATTERN = re.compile(r'(\d0|[一二三四五六七八九]十)(?=年代)')
YEAR_NUM_PATTERN = re.compile('[一二两三四五六七八九十百千0-9]{1,4}')

YEAR_PATTERNS = [YEAR_PATTERN, LIMIT_YEAR_PATTERN, BLUR_YEAR_1_PATTERN,
                 BLUR_YEAR_2_PATTERN, BLUR_YEAR_3_PATTERN, LUNAR_YEAR_PATTERN]

# =============== 月份相关模式 ===============

MONTH_PATTERN = re.compile(MONTH_STRING)
MONTH_NUM_PATTERN = re.compile(MONTH_NUM_STRING)  # 1~12
SPAN_MONTH_PATTERN = re.compile(
    '([第前后头]([一二两三四五六七八九十]|十[一二]|[1-9]|1[012])|首)(个)?月(份)?')
SOLAR_SEASON_PATTERN = re.compile(
    '((([第前后头][一二三四1-4两]|首)(个)?|[一二三四1-4两])季度[初中末]?)')
BLUR_MONTH_PATTERN = re.compile(BLUR_MONTH_STRING)
LUNAR_MONTH_PATTERN = re.compile(bracket(LUNAR_MONTH_STRING[:-1]) + '(?=月)')

MONTH_PATTERNS = [MONTH_PATTERN, SOLAR_SEASON_PATTERN, BLUR_MONTH_PATTERN,
                  SPAN_MONTH_PATTERN, LUNAR_MONTH_PATTERN, LIMIT_MONTH_PATTERN]

# =============== 日期相关模式 ===============

DAY_1_PATTERN = re.compile(DAY_STRING)
DAY_2_PATTERN = re.compile(r'(前|今|明|同一|当|后|大大前|大大后|大前|大后|昨|次)(?=[天日晚])')  # 昨晚9点
DAY_3_PATTERN = re.compile(BLUR_DAY_STRING)
LUNAR_DAY_PATTERN = re.compile(LUNAR_DAY_STRING + '(?!月)')
LUNAR_24ST_PATTERN = re.compile(SOLAR_TERM_STRING)
LUNAR_SEASON_PATTERN = re.compile('([春夏秋冬][季天]?)')

WEEK_1_PATTERN = re.compile('[前后][一二两三四五六七八九1-9](个)?' + WEEK_STRING)
WEEK_2_PATTERN = re.compile('[一两三四五六七八九1-9](个)?' + WEEK_STRING + '(之)?[前后]')
WEEK_3_PATTERN = re.compile('(上上|上|下下|下|本|这)(一)?(个)?' + WEEK_STRING)
WEEK_4_PATTERN = re.compile(WEEK_STRING + '[一二三四五六日末天]')
WEEK_5_PATTERN = re.compile(''.join(['第', WEEK_NUM_STRING, '(个)?', WEEK_STRING]))
YMD_SEGS = re.compile(r'[\-.·/]')
WEEK_NUM_PATTERN = re.compile(WEEK_NUM_STRING)

DAY_PATTERNS = [
    DAY_1_PATTERN, LUNAR_DAY_PATTERN, LUNAR_24ST_PATTERN,
    LUNAR_SEASON_PATTERN, DAY_3_PATTERN, WEEK_1_PATTERN,
    WEEK_2_PATTERN, WEEK_3_PATTERN, WEEK_4_PATTERN,
    WEEK_5_PATTERN, DAY_2_PATTERN]

# =============== 时间相关模式 ===============

HOUR_1_PATTERN = re.compile(
    HOUR_STRING.replace('[时点]', '') + r'(?=[时点])')
HOUR_LIMITATION_PATTERN = re.compile(BLUR_HOUR_STRING)

HOUR_PATTERNS = [HOUR_1_PATTERN, HOUR_LIMITATION_PATTERN]

# 分钟模式
MINUTE_PATTERN = re.compile(r'(?<=[时点])' + MIN_SEC_STRING + '(?=分)?')
LIMIT_MINUTE_PATTERN = re.compile(r'(?<=[时点])([123一二三]刻|半)')

MINUTE_PATTERNS = [MINUTE_PATTERN, LIMIT_MINUTE_PATTERN]

# 秒钟模式
SECOND_PATTERN = re.compile(r'(?<=分)' + MIN_SEC_STRING + '(?=秒)?')
HMS_SEGS = re.compile('[:：]')
SECOND_PATTERNS = [SECOND_PATTERN, ]

# =============== 节日模式 ===============

# 公历固定节日
FIXED_SOLAR_HOLIDAY_DICT = {
    # 国内
    '元旦': [1, 1], '妇女节': [3, 8], '女神节': [3, 8], '三八': [3, 8],
    '植树节': [3, 12], '五一': [5, 1], '劳动节': [5, 1], '青年节': [5, 4],
    '六一': [6, 1], '儿童节': [6, 1], '七一': [7, 1], '建党节': [7, 1],
    '八一': [8, 1], '建军节': [8, 1], '教师节': [9, 10], '国庆节': [10, 1],
    '十一': [10, 1], '国庆': [10, 1], '清明节': [4, 5],  # 清明节有误, 4~6 日

    # 西方
    '情人节': [2, 14], '愚人节': [4, 1], '万圣节': [10, 31], '圣诞': [12, 25],

    # 特定日
    '地球日': [4, 22], '护士节': [5, 12], '三一五': [3, 15], '消费者权益日': [3, 15],
    '三.一五': [3, 15], '三·一五': [3, 15], '双11': [11, 11], '双十一': [11, 11],
    '双12': [12, 12], '双十二': [12, 12], '618': [6, 18],
    '六.一八': [6, 18], '六一八': [6, 18], '六·一八': [6, 18],
}

# 农历固定节日
FIXED_LUNAR_HOLIDAY_DICT = {
    '春节': [1, 1], '大年初一': [1, 1], '大年初二': [1, 2], '大年初三': [1, 3],
    '大年初四': [1, 4], '大年初五': [1, 5], '大年初六': [1, 6], '大年初七': [1, 7],
    '大年初八': [1, 8], '大年初九': [1, 9], '大年初十': [1, 10],
    '元宵': [1, 15], '填仓节': [1, 25], '龙抬头': [2, 2],
    '上巳节': [3, 3], '寒食节': [4, 3],
    '浴佛节': [4, 8], '端午': [5, 5], '端阳': [5, 5], '姑姑节': [6, 6],
    '七夕': [7, 7], '中元': [7, 15], '财神节': [7, 22], '中秋': [8, 15],
    '重阳': [9, 9], '下元节': [10, 15], '寒衣节': [10, 1], '腊八': [12, 8],
    '除夕': [12, 30],
}

# 公历规律节日
REGULAR_SOLAR_HOLIDAY_DICT = {
    '母亲节': {'month': 5, 'week': 2, 'day': 7},  # 5 月第二个星期日
    '父亲节': {'month': 6, 'week': 3, 'day': 7},  # 6 月第三个星期日
    '感恩节': {'month': 11, 'week': 4, 'day': 4},  # 11 月第四个星期四
}

# 节日模式
YEAR_FIXED_SOLAR_FESTIVAL_PATTERN = re.compile(
    ''.join([bracket_absence(YEAR_STRING), FIXED_SOLAR_FESTIVAL]))

# 限定年 公历固定节日
LIMIT_YEAR_FIXED_SOLAR_FESTIVAL_PATTERN = re.compile(bracket(LIMIT_YEAR_STRING) + FIXED_SOLAR_FESTIVAL)

# 农历固定节日
YEAR_FIXED_LUNAR_FESTIVAL_PATTERN = re.compile(
    ''.join([bracket_absence(YEAR_STRING), LU_A, FIXED_LUNAR_FESTIVAL]))

# 限定年 农历固定节日
LIMIT_YEAR_FIXED_LUNAR_FESTIVAL_PATTERN = re.compile(
    ''.join([bracket(LIMIT_YEAR_STRING), LU_A, FIXED_LUNAR_FESTIVAL, absence(TIME_POINT_SUFFIX)]))

# 公历规律节日
YEAR_REGULAR_SOLAR_FESTIVAL_PATTERN = re.compile(
    bracket_absence(YEAR_STRING) + REGULAR_FOREIGN_FESTIVAL)

# 限定年 公历规律节日
LIMIT_YEAR_REGULAR_SOLAR_FESTIVAL_PATTERN = re.compile(
    bracket_absence(LIMIT_YEAR_STRING) + REGULAR_FOREIGN_FESTIVAL)

# =============== 时间跨度模式 ===============

# 周期性日期
PERIOD_TIME_PATTERN = re.compile(
    r'每((间)?隔)?([一二两三四五六七八九十0-9]+|半)?'
    r'(年|(个)?季度|(个)?月|(个)?(星期|礼拜)|(个)?周|((个)?工作)?日|天|(个)?(小时|钟头)|分(钟)?|秒(钟)?)')

# 由于 time_span 格式造成的时间单位缺失的检测
TIME_SPAN_POINT_COMPENSATION = re.compile(
    absence(BLUR_HOUR_STRING) +
    r'(?!:)[\d一二三四五六七八九十零]{1,2}[月日号点时]?(到|至|——|－－|--|~~|～～|—|－|-|~|～)'
    r'([\d一二三四五六七八九十零]{1,2}[月日号点时]|[\d一二三四五六七八九十零]{2,4}年)')

# 由于 time_span 格式造成的时间单位缺失的检测
TIME_SPAN_DELTA_COMPENSATION = re.compile(
    r'[\d一二三四五六七八九十百千万零]{1,10}(到|至|——|－－|--|~~|～～|—|－|-|~|～)'
    r'([\d一二三四五六七八九十百千万零]{1,10}(年|个月|周|(个)?(星期|礼拜)|日|天|(个)?(小时|钟头)|分钟|秒))')

TIME_DELTA_EXCEPTION_PATTERN = re.compile(
    r'(' + bracket(YEAR_STRING) + I + bracket(DAY_STRING) + r')')

# 特殊时间表述
SPECIAL_TIME_DELTA_PATTERN = re.compile(
    r'(' + SINGLE_NUM_STRING + r'天' + SINGLE_NUM_STRING + '[夜晚]|' + \
    SINGLE_NUM_STRING + '+[个载度]春秋|一年四季|大半(天|年|(个)?(月|小时|钟头)))')

SPECIAL_TIME_SPAN_PATTERN = re.compile(
    r'(今明两[天年]|全[天月年])')

# for TIME_SPAN pattern
FIRST_1_SPAN_PATTERN = re.compile(
    r'(?<=(从|自))([^起到至\-—~～]+)(?=(起|到|至|以来|开始|—|－|-|~|～))|'
    r'(?<=(从|自))([^起到至\-—~～]+)')
FIRST_2_SPAN_PATTERN = re.compile(r'(.+)(?=(——|--|~~|－－|～～))')
FIRST_3_SPAN_PATTERN = re.compile(r'([^起到至\-—~～]+)(?=(起|到|至|以来|开始|－|—|-|~|～))')
FIRST_4_SPAN_PATTERN = re.compile(r'(.+)(?=(之后|以后)$)')  # (之以)?后)$
FIRST_5_SPAN_PATTERN = re.compile(r'(.+)(?=(后)$)')  # (之以)?后)$

SECOND_0_SPAN_PATTERN = re.compile(r'(?<=(以来|开始|——|--|~~|－－|～～))(.+)')
SECOND_1_SPAN_PATTERN = re.compile(r'(?<=[起到至\-—~～－])([^起到至\-—~～－]+)(?=([之以]?前|止)$)')
SECOND_2_SPAN_PATTERN = re.compile(r'(?<=[起到至\-—~～－])([^起到至\-—~～－]+)')
SECOND_3_SPAN_PATTERN = re.compile(
    r'^((\d{1,2}|[一二两三四五六七八九十百千]+)[几多]?年(半)?(多)?|半年(多)?|几[十百千](多)?年)'
    r'(?=([之以]?前|止)$)')  # 此种匹配容易和 `三年以前` 相互矛盾，因此设置正则

# for delta span pattern
FIRST_DELTA_SPAN_PATTERN = re.compile(r'([^到至\-—~～]+)(?=(——|--|~~|～～|－－|到|至|－|—|-|~|～))')
SECOND_1_DELTA_SPAN_PATTERN = re.compile(r'(?<=(——|--|~~|～～|－－))([^到至\-—~～]+)')
SECOND_2_DELTA_SPAN_PATTERN = re.compile(r'(?<=[到至－—\-~～])([^到至－\-—~～]+)')

# =============== 年字符映射 ===============

YEAR_CHAR2NUM_MAP = {
    '零': '0', '〇': '0', '一': '1', '二': '2', '三': '3', '四': '4',
    '五': '5', '六': '6', '七': '7', '八': '8', '九': '9'}

# =============== 节气相关常量 ===============

# 20世纪 key值
_20_CENTURY_SOLAR_TERMS_KEY = [
    6.11, 20.84, 4.6295, 19.4599, 6.3826, 21.4155, 5.59, 20.888, 6.318, 21.86, 6.5, 22.2,
    7.928, 23.65, 8.35, 23.95, 8.44, 23.822, 9.098, 24.218, 8.218, 23.08, 7.9, 22.6]

# 21世纪 key值
_21_CENTURY_SOLAR_TERMS_KEY = [
    5.4055, 20.12, 3.87, 18.73, 5.63, 20.646, 4.81, 20.1, 5.52, 21.04, 5.678, 21.37, 7.108,
    22.83, 7.5, 23.13, 7.646, 23.042, 8.318, 23.438, 7.438, 22.36, 7.18, 21.94]

def get_solar_terms(st_key):
    """生成二十四节气字典"""
    # 二十四节气字典-- key值, 月份，(特殊年份，相差天数)
    solar_terms = {
        '小寒': [st_key[0], '1', (2019, -1), (1982, 1)],
        '大寒': [st_key[1], '1', (2082, 1)],
        '立春': [st_key[2], '2', (None, 0)],
        '雨水': [st_key[3], '2', (2026, -1)],
        '惊蛰': [st_key[4], '3', (None, 0)],
        '春分': [st_key[5], '3', (2084, 1)],
        '清明': [st_key[6], '4', (None, 0)],
        '谷雨': [st_key[7], '4', (None, 0)],
        '立夏': [st_key[8], '5', (1911, 1)],
        '小满': [st_key[9], '5', (2008, 1)],
        '芒种': [st_key[10], '6', (1902, 1)],
        '夏至': [st_key[11], '6', (None, 0)],
        '小暑': [st_key[12], '7', (2016, 1), (1925, 1)],
        '大暑': [st_key[13], '7', (1922, 1)],
        '立秋': [st_key[14], '8', (2002, 1)],
        '处暑': [st_key[15], '8', (None, 0)],
        '白露': [st_key[16], '9', (1927, 1)],
        '秋分': [st_key[17], '9', (None, 0)],
        '寒露': [st_key[18], '10', (2088, 0)],
        '霜降': [st_key[19], '10', (2089, 1)],
        '立冬': [st_key[20], '11', (2089, 1)],
        '小雪': [st_key[21], '11', (1978, 0)],
        '大雪': [st_key[22], '12', (1954, 1)],
        '冬至': [st_key[23], '12', (2021, -1), (1918, -1)]
    }
    return solar_terms

# 生成20世纪和21世纪的节气字典
_20_CENTURY_SOLAR_TERMS = get_solar_terms(_20_CENTURY_SOLAR_TERMS_KEY)
_21_CENTURY_SOLAR_TERMS = get_solar_terms(_21_CENTURY_SOLAR_TERMS_KEY)

# =============== 编译后的模式集合 ===============

# 所有编译后的正则模式
COMPILED_TIME_PARSER_PATTERNS = {
    # 基础模式
    'future_time_unit_pattern': FUTURE_TIME_UNIT_PATTERN,
    'future_time_unit_hms_pattern': FUTURE_TIME_UNIT_HMS_PATTERN,
    'chinese_char_pattern': CHINESE_CHAR_PATTERN_COMPILED,

    # 超模糊模式
    'super_blur_two_ymd_pattern': SUPER_BLUR_TWO_YMD_PATTERN,
    'super_blur_two_hms_pattern': SUPER_BLUR_TWO_HMS_PATTERN,
    'super_blur_several_days_pattern': SUPER_BLUR_SEVERAL_DAYS_PATTERN,

    # 时间跨度分割模式
    'time_span_seg_standard_year_month_to_year_month': TIME_SPAN_SEG_STANDARD_YEAR_MONTH_TO_YEAR_MONTH,
    'time_span_no_seg_standard_year_month_day': TIME_SPAN_NO_SEG_STANDARD_YEAR_MONTH_DAY,

    # 标准时间模式
    'standard_year_month_day_pattern': STANDARD_YEAR_MONTH_DAY_PATTERN,
    'standard_year_pattern': STANDARD_YEAR_PATTERN,

    # 年月日模式
    'year_month_day_pattern': YEAR_MONTH_DAY_PATTERN,
    'year_solar_season_pattern': YEAR_SOLAR_SEASON_PATTERN,
    'limit_year_solar_season_pattern': LIMIT_YEAR_SOLAR_SEASON_PATTERN,
    'limit_solar_season_pattern': LIMIT_SOLAR_SEASON_PATTERN,
    'year_span_month_pattern': YEAR_SPAN_MONTH_PATTERN,
    'limit_year_span_month_pattern': LIMIT_YEAR_SPAN_MONTH_PATTERN,
    'year_blur_month_pattern': YEAR_BLUR_MONTH_PATTERN,
    'limit_month_day_pattern': LIMIT_MONTH_DAY_PATTERN,
    'limit_month_blur_day_pattern': LIMIT_MONTH_BLUR_DAY_PATTERN,
    'limit_month_pattern': LIMIT_MONTH_PATTERN,
    'limit_year_blur_month_pattern': LIMIT_YEAR_BLUR_MONTH_PATTERN,
    'limit_year_month_day_pattern': LIMIT_YEAR_MONTH_DAY_PATTERN,
    'blur_year_pattern': BLUR_YEAR_PATTERN,
    'century_year_pattern': CENTURY_YEAR_PATTERN,
    'enum_day_pattern': ENUM_DAY_PATTERN,

    # 农历模式
    'lunar_year_month_day_pattern': LUNAR_YEAR_MONTH_DAY_PATTERN,
    'lunar_limit_year_month_day_pattern': LUNAR_LIMIT_YEAR_MONTH_DAY_PATTERN,
    'year_lunar_season_pattern': YEAR_LUNAR_SEASON_PATTERN,
    'limit_year_lunar_season_pattern': LIMIT_YEAR_LUNAR_SEASON_PATTERN,
    'year_24st_pattern': YEAR_24ST_PATTERN,
    'year_month_blur_day_pattern': YEAR_MONTH_BLUR_DAY_PATTERN,
    'limit_year_month_blur_day_pattern': LIMIT_YEAR_MONTH_BLUR_DAY_PATTERN,

    # 星期模式
    'standard_week_day_pattern': STANDARD_WEEK_DAY_PATTERN,
    'blur_week_pattern': BLUR_WEEK_PATTERN,
    'limit_week_pattern': LIMIT_WEEK_PATTERN,
    'year_week_pattern': YEAR_WEEK_PATTERN,
    'limit_year_week_pattern': LIMIT_YEAR_WEEK_PATTERN,

    # 特殊日期模式
    'num_month_num_pattern': NUM_MONTH_NUM_PATTERN,
    'limit_day_pattern': LIMIT_DAY_PATTERN,

    # 时分秒模式
    'hour_minute_second_pattern': HOUR_MINUTE_SECOND_PATTERN,
    'num_hour_minute_second_pattern': NUM_HOUR_MINUTE_SECOND_PATTERN,
    'blur_hour_pattern': BLUR_HOUR_PATTERN,
    'hour_limit_minute_pattern': HOUR_LIMIT_MINUTE_PATTERN,
    'single_num_pattern': SINGLE_NUM_PATTERN,

    # 时间增量模式
    'exception_standard_delta_pattern': EXCEPTION_STANDARD_DELTA_PATTERN,
    'ambivalent_delta_point_pattern': AMBIVALENT_DELTA_POINT_PATTERN,
    'delta_num_pattern': DELTA_NUM_PATTERN,
    'year_delta_pattern': YEAR_DELTA_PATTERN,
    'solar_season_delta_pattern': SOLAR_SEASON_DELTA_PATTERN,
    'month_delta_pattern': MONTH_DELTA_PATTERN,
    'workday_delta_pattern': WORKDAY_DELTA_PATTERN,
    'day_delta_pattern': DAY_DELTA_PATTERN,
    'week_delta_pattern': WEEK_DELTA_PATTERN,
    'hour_delta_pattern': HOUR_DELTA_PATTERN,
    'quarter_delta_pattern': QUARTER_DELTA_PATTERN,
    'minute_delta_pattern': MINUTE_DELTA_PATTERN,
    'second_delta_pattern': SECOND_DELTA_PATTERN,
    'standard_delta_pattern': STANDARD_DELTA_PATTERN,
    'weilai_delta2span_pattern': WEILAI_DELTA2SPAN_PATTERN,
    'guoqu_delta2span_pattern': GUOQU_DELTA2SPAN_PATTERN,
    'guo_delta2span_pattern': GUO_DELTA2SPAN_PATTERN,
    'law_delta_pattern': LAW_DELTA_PATTERN,

    # 时间增量转时间点模式
    'year_delta_point_pattern': YEAR_DELTA_POINT_PATTERN,
    'solar_season_delta_point_pattern': SOLAR_SEASON_DELTA_POINT_PATTERN,
    'month_delta_point_pattern': MONTH_DELTA_POINT_PATTERN,
    'workday_delta_point_pattern': WORKDAY_DELTA_POINT_PATTERN,
    'day_delta_point_pattern': DAY_DELTA_POINT_PATTERN,
    'week_delta_point_pattern': WEEK_DELTA_POINT_PATTERN,
    'hour_delta_point_pattern': HOUR_DELTA_POINT_PATTERN,
    'quarter_delta_point_pattern': QUARTER_DELTA_POINT_PATTERN,
    'minute_delta_point_pattern': MINUTE_DELTA_POINT_PATTERN,
    'second_delta_point_pattern': SECOND_DELTA_POINT_PATTERN,
    'year_order_delta_point_pattern': YEAR_ORDER_DELTA_POINT_PATTERN,
    'day_order_delta_point_pattern': DAY_ORDER_DELTA_POINT_PATTERN,

    # 年份相关模式
    'year_pattern': YEAR_PATTERN,
    'limit_year_pattern': LIMIT_YEAR_PATTERN,
    'blur_year_1_pattern': BLUR_YEAR_1_PATTERN,
    'blur_year_2_pattern': BLUR_YEAR_2_PATTERN,
    'blur_year_3_pattern': BLUR_YEAR_3_PATTERN,
    'lunar_year_pattern': LUNAR_YEAR_PATTERN,
    'century_pattern': CENTURY_PATTERN,
    'decade_pattern': DECADE_PATTERN,
    'year_num_pattern': YEAR_NUM_PATTERN,

    # 月份相关模式
    'month_pattern': MONTH_PATTERN,
    'month_num_pattern': MONTH_NUM_PATTERN,
    'span_month_pattern': SPAN_MONTH_PATTERN,
    'solar_season_pattern': SOLAR_SEASON_PATTERN,
    'blur_month_pattern': BLUR_MONTH_PATTERN,
    'lunar_month_pattern': LUNAR_MONTH_PATTERN,

    # 日期相关模式
    'day_1_pattern': DAY_1_PATTERN,
    'day_2_pattern': DAY_2_PATTERN,
    'day_3_pattern': DAY_3_PATTERN,
    'lunar_day_pattern': LUNAR_DAY_PATTERN,
    'lunar_24st_pattern': LUNAR_24ST_PATTERN,
    'lunar_season_pattern': LUNAR_SEASON_PATTERN,
    'week_1_pattern': WEEK_1_PATTERN,
    'week_2_pattern': WEEK_2_PATTERN,
    'week_3_pattern': WEEK_3_PATTERN,
    'week_4_pattern': WEEK_4_PATTERN,
    'week_5_pattern': WEEK_5_PATTERN,
    'ymd_segs': YMD_SEGS,
    'week_num_pattern': WEEK_NUM_PATTERN,

    # 时间相关模式
    'hour_1_pattern': HOUR_1_PATTERN,
    'hour_limitation_pattern': HOUR_LIMITATION_PATTERN,
    'minute_pattern': MINUTE_PATTERN,
    'limit_minute_pattern': LIMIT_MINUTE_PATTERN,
    'second_pattern': SECOND_PATTERN,
    'hms_segs': HMS_SEGS,

    # 节日模式
    'year_fixed_solar_festival_pattern': YEAR_FIXED_SOLAR_FESTIVAL_PATTERN,
    'limit_year_fixed_solar_festival_pattern': LIMIT_YEAR_FIXED_SOLAR_FESTIVAL_PATTERN,
    'year_fixed_lunar_festival_pattern': YEAR_FIXED_LUNAR_FESTIVAL_PATTERN,
    'limit_year_fixed_lunar_festival_pattern': LIMIT_YEAR_FIXED_LUNAR_FESTIVAL_PATTERN,
    'year_regular_solar_festival_pattern': YEAR_REGULAR_SOLAR_FESTIVAL_PATTERN,
    'limit_year_regular_solar_festival_pattern': LIMIT_YEAR_REGULAR_SOLAR_FESTIVAL_PATTERN,

    # 时间跨度模式
    'period_time_pattern': PERIOD_TIME_PATTERN,
    'time_span_point_compensation': TIME_SPAN_POINT_COMPENSATION,
    'time_span_delta_compensation': TIME_SPAN_DELTA_COMPENSATION,
    'time_delta_exception_pattern': TIME_DELTA_EXCEPTION_PATTERN,
    'special_time_delta_pattern': SPECIAL_TIME_DELTA_PATTERN,
    'special_time_span_pattern': SPECIAL_TIME_SPAN_PATTERN,

    # 时间跨度分割模式
    'first_1_span_pattern': FIRST_1_SPAN_PATTERN,
    'first_2_span_pattern': FIRST_2_SPAN_PATTERN,
    'first_3_span_pattern': FIRST_3_SPAN_PATTERN,
    'first_4_span_pattern': FIRST_4_SPAN_PATTERN,
    'first_5_span_pattern': FIRST_5_SPAN_PATTERN,
    'second_0_span_pattern': SECOND_0_SPAN_PATTERN,
    'second_1_span_pattern': SECOND_1_SPAN_PATTERN,
    'second_2_span_pattern': SECOND_2_SPAN_PATTERN,
    'second_3_span_pattern': SECOND_3_SPAN_PATTERN,
    'first_delta_span_pattern': FIRST_DELTA_SPAN_PATTERN,
    'second_1_delta_span_pattern': SECOND_1_DELTA_SPAN_PATTERN,
    'second_2_delta_span_pattern': SECOND_2_DELTA_SPAN_PATTERN,
}

# 模式列表
YEAR_PATTERNS_LIST = YEAR_PATTERNS
MONTH_PATTERNS_LIST = MONTH_PATTERNS
DAY_PATTERNS_LIST = DAY_PATTERNS
HOUR_PATTERNS_LIST = HOUR_PATTERNS
MINUTE_PATTERNS_LIST = MINUTE_PATTERNS
SECOND_PATTERNS_LIST = SECOND_PATTERNS

# 节日字典
FIXED_SOLAR_HOLIDAY_DICT_EXPORT = FIXED_SOLAR_HOLIDAY_DICT
FIXED_LUNAR_HOLIDAY_DICT_EXPORT = FIXED_LUNAR_HOLIDAY_DICT
REGULAR_SOLAR_HOLIDAY_DICT_EXPORT = REGULAR_SOLAR_HOLIDAY_DICT

# 年字符映射
YEAR_CHAR2NUM_MAP_EXPORT = YEAR_CHAR2NUM_MAP

# 节气相关常量
_20_CENTURY_SOLAR_TERMS_KEY_EXPORT = _20_CENTURY_SOLAR_TERMS_KEY
_21_CENTURY_SOLAR_TERMS_KEY_EXPORT = _21_CENTURY_SOLAR_TERMS_KEY
_20_CENTURY_SOLAR_TERMS_EXPORT = _20_CENTURY_SOLAR_TERMS
_21_CENTURY_SOLAR_TERMS_EXPORT = _21_CENTURY_SOLAR_TERMS
GET_SOLAR_TERMS_FUNCTION = get_solar_terms
